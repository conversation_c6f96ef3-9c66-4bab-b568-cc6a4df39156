# Technology Stack

## Core Technologies
- **Documentation Format**: Markdown (.md)
- **File Organization**: Chronological naming convention (YYYY.MM.DD)
- **Content Types**: 
  - Medical documentation
  - Research notes
  - Communication logs
  - Image assets (PNG, JPG)
  - PDF documents

## Structure Pattern
- **Categorized Prefixes**: RESEARCH, MEDICAL, LOG, GPT, NAV, CONTEXT, APPOINTMENT
- **Hierarchical Organization**: Master index with cross-references
- **Version Control**: Revision tracking (r1, r2 suffixes)
- **Asset Management**: Separate directories for images and PDFs

## Documentation Standards
- **Language**: Norwegian (primary) with English technical terms
- **Format**: Structured markdown with consistent headers
- **Cross-referencing**: Internal linking between related documents
- **Metadata**: Timestamps and update tracking
